"use client";
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>it<PERSON>,
} from "@/components/ui/sheet";

import { useState, useRef, useEffect } from "react";
import { X, Check, Plus } from "react-feather";
import { useRouter } from "next/navigation";
import { Input } from "./ui/input";
import SidebarLocationDropdown from "./SidebarLocationDropdown";
import ProfileDropdown from "./ProfileDropdown";
import { useGoogleMaps } from "@/context/GoogleMapsContext";
import { FilterSearchManager, ProfileSearchBy } from "@/services/filtersServices";
import { generateFileUrl } from "@/lib/utils";

const Category = [
  "My Feed",
  "Music",
  "Literature",
  "Art",
  "Theatre & Performance",
  "Film & Photography",
  "Multidisciplinary",
  "Groups",
];

const Dateofpublishing = ["Past 24 hours", "Past week", "Past month"];
const ProfileName = ["Tom Li", "Kim Li 2"];
const Postlocation = ["United Kingdom", "United States", "United States 2"];

interface FilterProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyFilter?: (filters: {
    categories?: string[];
    dateOfPublishing?: string;
    profileName?: string[];
    postLocation?: string;
    location?: string[];
    user_id?: string[];
    lensProfiles?: Array<{
      category: string;
      usernames: string[];
    }>;
  }) => void;
}

export function Filter({ open, onOpenChange, onApplyFilter }: FilterProps) {
  const router = useRouter();
  const [activeSheet, setActiveSheet] = useState(1);

  const showSheet = (sheetNumber: number) => {
    setActiveSheet(sheetNumber);
  };

  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!baseUrl) return undefined;

  //   if (!postFile) {
  //     return undefined;
  //   }

  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //     return postFile;
  //   }

  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  //   for Category - now supports multiple selection
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);

  const handleItemClickCategory = (index: number) => {
    setSelectedCategories((prev) => {
      if (prev.includes(index)) {
        // Remove if already selected
        return prev.filter((i) => i !== index);
      } else {
        // Add if not selected
        return [...prev, index];
      }
    });
  };

  //   for Date Of
  const [SelectedProfileName, setSelectedProfileName] = useState<number | null>(null);

  const handleItemClickProfileName = (index: number) => {
    setSelectedProfileName(index); // Set the clicked item as the selected one
  };

  //   for Date Of
  const [SelectedDate, setSelectedDate] = useState<number | null>(null);

  const handleItemClickDate = (index: number) => {
    setSelectedDate(index); // Set the clicked item as the selected one
  };
  //   for postLocation
  const [selectedPostLocation, setSelectedPostLocation] = useState<number | null>(null);

  const handleItemClickPostLocation = (index: number) => {
    setSelectedPostLocation(index); // Set the clicked item as the selected one
  };

  // Google Maps location functionality
  const locationInputRef = useRef<HTMLInputElement>(null);
  const [locationInput, setLocationInput] = useState("");
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [placePredictions, setPlacePredictions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showPlacesDropdown, setShowPlacesDropdown] = useState(false);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceLoaded = useRef(false);

  // Profile search functionality
  const profileInputRef = useRef<HTMLInputElement>(null);
  const [profileInput, setProfileInput] = useState("");
  const [selectedProfiles, setSelectedProfiles] = useState<any[]>([]);
  const [profilePredictions, setProfilePredictions] = useState<any[]>([]);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [profileSearchLoading, setProfileSearchLoading] = useState(false);

  // Use Google Maps context
  const { isLoaded, placesService } = useGoogleMaps();

  // Initialize Google Maps API
  useEffect(() => {
    console.log(
      "filter.tsx: Google Maps initialization - isLoaded:",
      isLoaded,
      "placesService:",
      placesService
    );
    // When the Google Maps API is loaded via the context, set up the autocomplete service
    if (isLoaded && !placesServiceLoaded.current) {
      console.log("filter.tsx: Setting up Google Maps autocomplete service");
      placesServiceLoaded.current = true;
      // Create autocomplete service directly
      autocompleteService.current = new google.maps.places.AutocompleteService();
      console.log("filter.tsx: Autocomplete service created:", autocompleteService.current);
    }
  }, [isLoaded, placesService]);

  // Handle location input change and fetch place predictions
  const handleLocationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    console.log("filter.tsx: Location input changed:", value);
    setLocationInput(value);

    if (value.trim() === "") {
      setPlacePredictions([]);
      setShowPlacesDropdown(false);
      return;
    }

    console.log(
      "filter.tsx: Checking autocomplete service:",
      autocompleteService.current,
      "loaded:",
      placesServiceLoaded.current
    );
    if (autocompleteService.current && placesServiceLoaded.current) {
      console.log("filter.tsx: Making places prediction request");
      autocompleteService.current.getPlacePredictions(
        {
          input: value,
          types: ["(cities)"],
        },
        (predictions, status) => {
          console.log(
            "filter.tsx: Places prediction response - status:",
            status,
            "predictions:",
            predictions
          );
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setPlacePredictions(predictions);
            setShowPlacesDropdown(true);
          } else {
            setPlacePredictions([]);
            setShowPlacesDropdown(false);
          }
        }
      );
    } else {
      console.log("filter.tsx: Autocomplete service not ready");
    }
  };

  // Select a place from the dropdown
  const selectPlace = (place: google.maps.places.AutocompletePrediction) => {
    console.log("filter.tsx: selectPlace called with place:", place);
    const locationName = place.description;
    console.log("filter.tsx: locationName:", locationName);
    console.log("filter.tsx: current selectedLocations:", selectedLocations);

    // Check if location is already selected
    if (!selectedLocations.includes(locationName)) {
      console.log("filter.tsx: Adding location to selectedLocations");
      setSelectedLocations((prev) => {
        const newLocations = [...prev, locationName];
        console.log("filter.tsx: New selectedLocations:", newLocations);
        return newLocations;
      });
    } else {
      console.log("filter.tsx: Location already selected");
    }

    // Clear the input and hide dropdown
    setLocationInput("");
    setShowPlacesDropdown(false);
  };

  // Remove a selected location
  const removeLocation = (locationToRemove: string) => {
    setSelectedLocations((prev) => prev.filter((loc) => loc !== locationToRemove));
  };

  // Handle profile input change and search profiles
  const handleProfileInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setProfileInput(value);

    if (value.trim() === "") {
      setProfilePredictions([]);
      setShowProfileDropdown(false);
      return;
    }

    if (value.length < 2) return; // Wait for at least 2 characters

    setProfileSearchLoading(true);
    try {
      const response = await FilterSearchManager.getInstance().GetProfileByFilters({
        payload: {
          filterBy: ProfileSearchBy.PROFILE_NAME,
          searchTerm: value,
          limit: 10,
        },
      });
      console.log("filter.tsx: Profile search response:", response);

      // Merge profiles and lens data
      const mergedProfiles = [];

      // Add regular profiles
      if (response.profiles && response.profiles.length > 0) {
        mergedProfiles.push(
          ...response.profiles.map((profile) => ({
            ...profile,
            isLensProfile: false,
          }))
        );
      }

      // Add lens profiles
      if (response.lens && response.lens.length > 0) {
        mergedProfiles.push(
          ...response.lens.map((lensName) => ({
            id: lensName.user_name,
            profile_name: lensName.user_name,
            Category: lensName.category,
            avatarSmall: null, // Lens profiles don't have avatars in this context
            location: "Lens Profile",
            isLensProfile: true,
          }))
        );
      }

      // Sort merged profiles alphabetically by profile_name
      const sortedProfiles = mergedProfiles.sort((a, b) => {
        const nameA = (a.profile_name || "").toLowerCase();
        const nameB = (b.profile_name || "").toLowerCase();
        return nameA.localeCompare(nameB);
      });

      if (sortedProfiles.length > 0) {
        setProfilePredictions(sortedProfiles);
        setShowProfileDropdown(true);
      } else {
        setProfilePredictions([]);
        setShowProfileDropdown(false);
      }
    } catch (error) {
      console.error("Error searching profiles:", error);
      setProfilePredictions([]);
      setShowProfileDropdown(false);
    } finally {
      setProfileSearchLoading(false);
    }
  };

  // Select a profile from the dropdown
  const selectProfile = (profile: any) => {
    console.log("filter.tsx: selectProfile called with profile:", profile);
    console.log("filter.tsx: current selectedProfiles:", selectedProfiles);

    // Check if profile is already selected
    if (!selectedProfiles.find((p) => p.id === profile.id)) {
      console.log("filter.tsx: Adding profile to selectedProfiles");
      setSelectedProfiles((prev) => {
        const newProfiles = [...prev, profile];
        console.log("filter.tsx: New selectedProfiles:", newProfiles);
        return newProfiles;
      });
    } else {
      console.log("filter.tsx: Profile already selected");
    }

    // Clear the input and hide dropdown
    setProfileInput("");
    setShowProfileDropdown(false);
  };

  // Remove a selected profile
  const removeProfile = (profileToRemove: any) => {
    setSelectedProfiles((prev) => prev.filter((profile) => profile.id !== profileToRemove.id));
  };

  const handleClearFilters = () => {
    // Reset all filter states first
    setSelectedCategories([]);
    setSelectedDate(null);
    setSelectedProfileName(null);
    setSelectedPostLocation(null);
    setSelectedLocations([]);
    setSelectedProfiles([]);
    setLocationInput("");
    setProfileInput("");
    setShowPlacesDropdown(false);
    setShowProfileDropdown(false);

    // Apply empty filters to clear all applied filters
    if (onApplyFilter) {
      onApplyFilter({
        categories: [],
        dateOfPublishing: undefined,
        profileName: undefined,
        postLocation: undefined,
        location: undefined,
        user_id: undefined,
        lensProfiles: undefined,
      });
      onOpenChange(false);
    }
  };

  // Check if any filter is selected
  const hasAnyFilterSelected = () => {
    return (
      selectedCategories.length > 0 ||
      SelectedDate !== null ||
      SelectedProfileName !== null ||
      selectedPostLocation !== null ||
      selectedLocations.length > 0 ||
      selectedProfiles.length > 0
    );
  };

  // Handle Done button click - return to main sheet
  const handleDone = () => {
    setActiveSheet(1);
  };

  // Handle Apply button click - apply all filters
  const handleApply = () => {
    if (onApplyFilter) {
      const filters = {
        categories:
          selectedCategories.length > 0
            ? selectedCategories.map((index) => Category[index])
            : undefined,
        dateOfPublishing: SelectedDate !== null ? Dateofpublishing[SelectedDate] : undefined,
        profileName:
          selectedProfiles.length > 0
            ? selectedProfiles.map((profile) => profile.profile_name).filter((name) => name)
            : undefined,
        postLocation:
          selectedPostLocation !== null ? Postlocation[selectedPostLocation] : undefined,
        location: selectedLocations.length > 0 ? selectedLocations : undefined,
        user_id:
          selectedProfiles.length > 0
            ? selectedProfiles
                .filter((profile) => !profile.isLensProfile) // Only include non-lens profiles for user_id
                .map((profile) => profile.id || profile.user_id)
                .filter((id) => id)
            : undefined,

        lensProfiles:
          selectedProfiles.length > 0
            ? (() => {
                // Get lens profiles with their categories
                const lensProfiles = selectedProfiles.filter((profile) => profile.isLensProfile);

                if (lensProfiles.length === 0) return undefined;

                // Group lens profiles by their category
                const categoryGroups: { [key: string]: string[] } = {};

                lensProfiles.forEach((profile) => {
                  const category = profile.Category || "uncategorized";
                  if (!categoryGroups[category]) {
                    categoryGroups[category] = [];
                  }
                  if (profile.profile_name) {
                    categoryGroups[category].push(profile.profile_name);
                  }
                });

                // Convert to array format
                const result = Object.entries(categoryGroups).map(([category, usernames]) => ({
                  category,
                  usernames,
                }));

                console.log("filter.tsx: Grouped lens profiles by category:", result);
                return result;
              })()
            : undefined,
      };

      console.log("filter.tsx: Applying filters:", filters);
      onApplyFilter(filters);
      onOpenChange(false);
    } else {
      // Fallback to navigation if no callback provided
      if (selectedCategories.length > 0) {
        onOpenChange(false);
        router.push(`/selectedCategory/${Category[selectedCategories[0]]}`);
      }
    }
  };
  return (
    <div>
      <Sheet open={open} onOpenChange={onOpenChange} key="right">
        <SheetContent className="w-[22rem] max-md:w-full p-0" style={{}} side="right">
          {activeSheet === 1 && (
            <SheetHeader>
              <SheetTitle>
                <div
                  className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 "
                  style={{ zIndex: 999999 }}
                >
                  <div className=" cursor-pointer" onClick={() => onOpenChange(false)}>
                    <X />
                  </div>

                  <p className="text-lg font-bold text-titleLabel">Filter Posts</p>
                  <p
                    className={
                      hasAnyFilterSelected()
                        ? "text-lg text-primary font-bold cursor-pointer"
                        : "text-lg text-borderColor font-bold cursor-pointer"
                    }
                    onClick={handleClearFilters}
                  >
                    Clear
                  </p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  <div className="filter-list" onClick={() => showSheet(2)}>
                    <p className="text-titleLabel">Category</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">
                        {selectedCategories.length === 0
                          ? "Any"
                          : `${selectedCategories.length} selected`}
                      </p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                  <div className="filter-list mt-4" onClick={() => showSheet(3)}>
                    <p className="text-titleLabel">Date of publishing</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">
                        {SelectedDate !== null ? Dateofpublishing[SelectedDate] : "Any"}
                      </p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                  <div className="filter-list mt-4" onClick={() => showSheet(4)}>
                    <p className="text-titleLabel">Post location</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">
                        {selectedLocations.length === 0
                          ? "Any"
                          : `${selectedLocations.length} selected`}
                      </p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                  <div className="filter-list mt-4" onClick={() => showSheet(5)}>
                    <p className="text-titleLabel">Profile name</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">
                        {selectedProfiles.length === 0
                          ? "Any"
                          : `${selectedProfiles.length} selected`}
                      </p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>

                  {/* Apply Button - Show only when filters are selected */}
                  {hasAnyFilterSelected() && (
                    <div className="mt-8 px-4 pb-4">
                      <button
                        onClick={handleApply}
                        className="w-full bg-primary text-white py-3 px-4 rounded-lg font-semibold text-base hover:bg-primary/90 transition-colors"
                      >
                        Apply Filters
                      </button>
                    </div>
                  )}
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 2 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Category</p>
                  <p
                    className="text-base text-primary font-bold cursor-pointer"
                    onClick={handleDone}
                  >
                    Done
                  </p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  {Category.map((item, index) => {
                    return (
                      <div
                        key={index}
                        className="filter-list mt-4"
                        onClick={() => handleItemClickCategory(index)}
                      >
                        <p className="text-titleLabel">{item}</p>

                        <div className="row gap-3 text-w">
                          {selectedCategories.includes(index) && (
                            <Check color="#25282B" strokeWidth="1.6px" />
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 3 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Date of publishing</p>
                  <p
                    className="text-base text-primary font-bold cursor-pointer"
                    onClick={handleDone}
                  >
                    Done
                  </p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  {Dateofpublishing.map((item, index) => {
                    return (
                      <div
                        className="filter-list mt-4"
                        key={index}
                        onClick={() => handleItemClickDate(index)}
                      >
                        <p className="text-titleLabel">{item}</p>

                        <div className="row gap-3 text-w">
                          {SelectedDate === index && <Check color="#25282B" strokeWidth="1.6px" />}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 4 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Post location</p>
                  <p
                    className="text-base text-primary font-bold cursor-pointer"
                    onClick={handleDone}
                  >
                    Done
                  </p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem] px-4">
                  {/* Selected Locations Display */}
                  {selectedLocations.length > 0 && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-titleLabel mb-2">
                        Selected Locations:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {selectedLocations.map((location, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-2 bg-primary/10 text-primary px-3 py-1 rounded-full text-sm"
                          >
                            <span>{location}</span>
                            <button
                              onClick={() => removeLocation(location)}
                              className="hover:bg-primary/20 rounded-full p-0.5 transition-colors"
                              aria-label={`Remove ${location}`}
                            >
                              <X size={14} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Location Input */}
                  <div className="relative">
                    <Input
                      ref={locationInputRef}
                      placeholder="Search for a location..."
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                      value={locationInput}
                      onChange={handleLocationInputChange}
                      aria-label="location"
                    />
                    <div className="">
                      <SidebarLocationDropdown
                        predictions={placePredictions}
                        onSelect={selectPlace}
                        inputRef={locationInputRef}
                        isVisible={showPlacesDropdown && placePredictions.length > 0}
                      />
                    </div>
                  </div>
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 5 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Profile Name</p>
                  <p
                    className="text-base text-primary font-bold cursor-pointer"
                    onClick={handleDone}
                  >
                    Done
                  </p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem] px-4">
                  {/* Selected Profiles Display */}
                  {selectedProfiles.length > 0 && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-titleLabel mb-2">Selected Profiles:</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedProfiles.map((profile, index) => (
                          <div
                            key={profile.id || index}
                            className="flex items-center gap-2 bg-primary/10 text-primary px-3 py-1 rounded-full text-sm"
                          >
                            <div className="flex items-center gap-2">
                              {profile.avatarSmall ? (
                                <img
                                  src={generateFileUrl(profile.avatarSmall) || "/assets/noimg.png"}
                                  alt={profile.profile_name}
                                  className="w-4 h-4 rounded-full object-cover"
                                />
                              ) : profile.isLensProfile ? (
                                <div className="w-4 h-4 rounded-full bg-purple-100 flex items-center justify-center">
                                  <span className="text-xs font-medium text-purple-600">L</span>
                                </div>
                              ) : (
                                <div className="w-4 h-4 rounded-full bg-gray-300 flex items-center justify-center">
                                  <span className="text-xs font-medium text-gray-600">
                                    {profile.profile_name?.charAt(0)?.toUpperCase() || "?"}
                                  </span>
                                </div>
                              )}
                              <span className={profile.isLensProfile ? "text-purple-600" : ""}>
                                {profile.profile_name}
                                {profile.isLensProfile && (
                                  <span className="ml-1 text-xs bg-purple-100 text-purple-600 px-1 py-0.5 rounded">
                                    Lens
                                  </span>
                                )}
                              </span>
                            </div>
                            <button
                              onClick={() => removeProfile(profile)}
                              className="hover:bg-primary/20 rounded-full p-0.5 transition-colors"
                              aria-label={`Remove ${profile.profile_name}`}
                            >
                              <X size={14} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Profile Search Input */}
                  <div className="relative">
                    <Input
                      ref={profileInputRef}
                      placeholder="Search for a profile..."
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                      value={profileInput}
                      onChange={handleProfileInputChange}
                      aria-label="profile search"
                    />
                    <div className="z-[0]">
                      <ProfileDropdown
                        profiles={profilePredictions}
                        onSelect={selectProfile}
                        inputRef={profileInputRef}
                        isVisible={
                          showProfileDropdown &&
                          (profilePredictions.length > 0 || profileSearchLoading)
                        }
                        loading={profileSearchLoading}
                      />
                    </div>
                  </div>
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}
